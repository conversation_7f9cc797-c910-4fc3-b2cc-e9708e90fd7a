#version 450

#extension GL_EXT_control_flow_attributes : enable

#include "generic_head.comp"
#include "types.comp"

layout(constant_id = 0) const uint BLOCK_SIZE = 32;
layout(local_size_x_id = 0, local_size_y = 1, local_size_z = 1) in;

// In this shader Y = softmax(X) and X is not provided as input.

layout (binding = 0) readonly buffer G {A_TYPE data_g[];};
layout (binding = 1) readonly buffer Y {B_TYPE data_y[];};
layout (binding = 2) buffer D {D_TYPE data_d[];};

shared FLOAT_TYPE sum_yg[BLOCK_SIZE];

void main() {
    const uint row = gl_WorkGroupID.z * 262144 + gl_WorkGroupID.y * 512 + gl_WorkGroupID.x;
    const uint tid = gl_LocalInvocationID.x;

    FLOAT_TYPE scale = p.param1;

    // partial sums for thread in warp
    sum_yg[tid] = FLOAT_TYPE(0.0f);

    [[unroll]] for (uint col = tid; col < p.KX; col += BLOCK_SIZE) {
        const FLOAT_TYPE gi = FLOAT_TYPE(data_g[row*p.KX + col]);
        const FLOAT_TYPE yi = FLOAT_TYPE(data_y[row*p.KX + col]);
        sum_yg[tid] += yi * gi;
    }

    // sum up partial sums and write back result
    barrier();
    [[unroll]] for (uint s = BLOCK_SIZE / 2; s > 0; s >>= 1) {
        if (tid < s) {
            sum_yg[tid] += sum_yg[tid + s];
        }
        barrier();
    }

    const FLOAT_TYPE dot_yg = sum_yg[0];

    [[unroll]] for (uint col = tid; col < p.KX; col += BLOCK_SIZE) {
        data_d[row*p.KX + col] = D_TYPE(scale
            * (FLOAT_TYPE(data_g[row*p.KX + col]) - dot_yg)
            * FLOAT_TYPE(data_y[row*p.KX + col]));
    }
}
