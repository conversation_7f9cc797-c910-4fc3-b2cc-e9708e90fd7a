{"version": 4, "configurePresets": [{"name": "base", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build-${presetName}", "cacheVariables": {"CMAKE_EXPORT_COMPILE_COMMANDS": "ON", "CMAKE_INSTALL_RPATH": "$ORIGIN;$ORIGIN/.."}}, {"name": "sycl-base", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build-${presetName}", "cacheVariables": {"CMAKE_EXPORT_COMPILE_COMMANDS": "ON", "CMAKE_CXX_COMPILER": "icx", "CMAKE_C_COMPILER": "cl", "GGML_SYCL": "ON", "CMAKE_INSTALL_RPATH": "$ORIGIN;$ORIGIN/.."}}, {"name": "debug", "hidden": true, "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "release", "hidden": true, "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"name": "reldbg", "hidden": true, "cacheVariables": {"CMAKE_BUILD_TYPE": "RelWithDebInfo"}}, {"name": "static", "hidden": true, "cacheVariables": {"GGML_STATIC": "ON"}}, {"name": "sycl_f16", "hidden": true, "cacheVariables": {"GGML_SYCL_F16": "ON"}}, {"name": "vulkan", "hidden": true, "cacheVariables": {"GGML_VULKAN": "ON"}}, {"name": "x64-windows-llvm", "hidden": true, "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": "${sourceDir}/cmake/x64-windows-llvm.cmake"}}, {"name": "arm64-windows-llvm", "hidden": true, "architecture": {"value": "arm64", "strategy": "external"}, "toolset": {"value": "host=x64", "strategy": "external"}, "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": "${sourceDir}/cmake/arm64-windows-llvm.cmake"}}, {"name": "arm64-apple-clang", "hidden": true, "architecture": {"value": "arm64", "strategy": "external"}, "toolset": {"value": "host=x64", "strategy": "external"}, "cacheVariables": {"CMAKE_TOOLCHAIN_FILE": "${sourceDir}/cmake/arm64-apple-clang.cmake"}}, {"name": "x64-linux-gcc", "hidden": true, "cacheVariables": {"CMAKE_C_COMPILER": "gcc", "CMAKE_CXX_COMPILER": "g++"}}, {"name": "x64-linux-gcc-debug", "inherits": ["base", "x64-linux-gcc", "debug"]}, {"name": "x64-linux-gcc-release", "inherits": ["base", "x64-linux-gcc", "release"]}, {"name": "x64-linux-gcc-reldbg", "inherits": ["base", "x64-linux-gcc", "reldbg"]}, {"name": "x64-linux-gcc+static-release", "inherits": ["base", "x64-linux-gcc", "release", "static"]}, {"name": "arm64-windows-llvm-debug", "inherits": ["base", "arm64-windows-llvm", "debug"]}, {"name": "arm64-windows-llvm-release", "inherits": ["base", "arm64-windows-llvm", "reldbg"]}, {"name": "arm64-windows-llvm+static-release", "inherits": ["base", "arm64-windows-llvm", "reldbg", "static"]}, {"name": "arm64-apple-clang-debug", "inherits": ["base", "arm64-apple-clang", "debug"]}, {"name": "arm64-apple-clang-release", "inherits": ["base", "arm64-apple-clang", "reldbg"]}, {"name": "arm64-apple-clang+static-release", "inherits": ["base", "arm64-apple-clang", "reldbg", "static"]}, {"name": "x64-windows-llvm-debug", "inherits": ["base", "x64-windows-llvm", "debug"]}, {"name": "x64-windows-llvm-release", "inherits": ["base", "x64-windows-llvm", "release"]}, {"name": "x64-windows-llvm-reldbg", "inherits": ["base", "x64-windows-llvm", "reldbg"]}, {"name": "x64-windows-llvm+static-release", "inherits": ["base", "x64-windows-llvm", "reldbg", "static"]}, {"name": "x64-windows-msvc-debug", "inherits": ["base", "debug"]}, {"name": "x64-windows-msvc-release", "inherits": ["base", "reldbg"]}, {"name": "x64-windows-msvc+static-release", "inherits": ["base", "reldbg", "static"]}, {"name": "x64-windows-sycl-debug", "inherits": ["sycl-base", "debug"]}, {"name": "x64-windows-sycl-debug-f16", "inherits": ["sycl-base", "debug", "sycl_f16"]}, {"name": "x64-windows-sycl-release", "inherits": ["sycl-base", "release"]}, {"name": "x64-windows-sycl-release-f16", "inherits": ["sycl-base", "release", "sycl_f16"]}, {"name": "x64-windows-vulkan-debug", "inherits": ["base", "vulkan", "debug"]}, {"name": "x64-windows-vulkan-release", "inherits": ["base", "vulkan", "release"]}]}