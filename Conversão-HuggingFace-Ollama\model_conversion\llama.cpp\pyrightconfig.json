{
  "extraPaths": ["gguf-py"],
  "pythonVersion": "3.9",
  "pythonPlatform": "All",
  "reportUnusedImport": "warning",
  "reportDuplicateImport": "error",
  "reportDeprecated": "warning",
  "reportUnnecessaryTypeIgnoreComment": "information",
  "disableBytesTypePromotions": false, // TODO: change once Python 3.12 is the minimum
  "executionEnvironments": [
    {
      // TODO: make this version override work correctly
      "root": "gguf-py",
      "pythonVersion": "3.8",
    },
    {
      // uses match expressions in steps.py
      "root": "tools/server/tests",
      "pythonVersion": "3.10",
    },
  ],
 }
