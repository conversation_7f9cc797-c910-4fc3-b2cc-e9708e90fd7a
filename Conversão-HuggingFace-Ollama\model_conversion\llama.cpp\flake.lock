{"nodes": {"flake-parts": {"inputs": {"nixpkgs-lib": "nixpkgs-lib"}, "locked": {"lastModified": 1730504689, "narHash": "sha256-hgmguH29K2fvs9szpq2r3pz2/8cJd2LPS+b4tfNFCwE=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "506278e768c2a08bec68eb62932193e341f55c90", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1732014248, "narHash": "sha256-y/MEyuJ5oBWrWAic/14LaIr/u5E0wRVzyYsouYY3W6w=", "owner": "NixOS", "repo": "nixpkgs", "rev": "23e89b7da85c3640bbc2173fe04f4bd114342367", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-lib": {"locked": {"lastModified": 1730504152, "narHash": "sha256-lXvH/vOfb4aGYyvFmZK/HlsNsr/0CVWlwYvo2rxJk3s=", "type": "tarball", "url": "https://github.com/NixOS/nixpkgs/archive/cc2f28000298e1269cea6612cd06ec9979dd5d7f.tar.gz"}, "original": {"type": "tarball", "url": "https://github.com/NixOS/nixpkgs/archive/cc2f28000298e1269cea6612cd06ec9979dd5d7f.tar.gz"}}, "root": {"inputs": {"flake-parts": "flake-parts", "nixpkgs": "nixpkgs"}}}, "root": "root", "version": 7}