#version 450

#include "types.comp"
#include "generic_binary_head.comp"

layout(local_size_x = 512, local_size_y = 1, local_size_z = 1) in;

void main() {
    const uint idx = gl_GlobalInvocationID.z * 262144 + gl_GlobalInvocationID.y * 512 + gl_GlobalInvocationID.x;
    const int dim = p.param3;

    if (idx >= p.ne) {
        return;
    }

    const uint i3 = idx / (p.ne22*p.ne21*p.ne20);
    const uint i3_offset = i3 * p.ne22*p.ne21*p.ne20;
    const uint i2 = (idx - i3_offset) / (p.ne21*p.ne20);
    const uint i2_offset = i2*p.ne21*p.ne20;
    const uint i1 = (idx - i3_offset - i2_offset) / p.ne20;
    const uint i0 = idx - i3_offset - i2_offset - i1*p.ne20;

    uint o[4] = {0, 0, 0, 0};
    o[dim] = dim == 0 ? p.ne00 : (dim == 1 ? p.ne01 : (dim == 2 ? p.ne02 : p.ne03));

    const uint src0_idx = i3*p.nb03 + i2*p.nb02 + i1*p.nb01 + i0*p.nb00;
    const uint src1_idx = (i3 - o[3])*p.nb13 + (i2 - o[2])*p.nb12 + (i1 - o[1])*p.nb11 + (i0 - o[0])*p.nb10;
    const uint dst_idx = i3*p.nb23 + i2*p.nb22 + i1*p.nb21 + i0*p.nb20;

    const bool is_src0 = i0 < p.ne00 && i1 < p.ne01 && i2 < p.ne02 && i3 < p.ne03;

#ifndef OPTIMIZATION_ERROR_WORKAROUND
    data_d[get_doffset() + dst_idx] = D_TYPE(is_src0 ? data_a[get_aoffset() + src0_idx] : data_b[get_boffset() + src1_idx]);
#else
    if (is_src0) {
        data_d[get_doffset() + dst_idx] = data_a[get_aoffset() + src0_idx];
    } else {
        data_d[get_doffset() + dst_idx] = data_b[get_boffset() + src1_idx];
    }
#endif
}
