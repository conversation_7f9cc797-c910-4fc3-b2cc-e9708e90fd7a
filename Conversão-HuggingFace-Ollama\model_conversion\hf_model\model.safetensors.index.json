{"metadata": {"total_parameters": 8387373392, "total_size": 15699956384}, "weight_map": {"model.audio_tower.conformer.0.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.0.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.1.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.10.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.11.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.2.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.3.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.4.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.5.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.6.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.7.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.8.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.attention.attn.k_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.attention.attn.per_dim_scale": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.attention.attn.q_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.attention.attn.relative_position_embedding.pos_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.attention.attn.v_proj.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.attention.post.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.attention.post_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.attention.pre_attn_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.ffw_layer_end.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.ffw_layer_end.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.ffw_layer_end.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.ffw_layer_end.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.ffw_layer_start.ffw_layer_1.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.ffw_layer_start.ffw_layer_2.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.ffw_layer_start.post_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.ffw_layer_start.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.lconv1d.conv_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.lconv1d.depthwise_conv1d.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.lconv1d.linear_end.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.lconv1d.linear_start.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.lconv1d.pre_layer_norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.conformer.9.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.subsample_conv_projection.conv_0.conv.weight": "model-00001-of-00004.safetensors", "model.audio_tower.subsample_conv_projection.conv_0.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.subsample_conv_projection.conv_1.conv.weight": "model-00001-of-00004.safetensors", "model.audio_tower.subsample_conv_projection.conv_1.norm.weight": "model-00001-of-00004.safetensors", "model.audio_tower.subsample_conv_projection.input_proj_linear.weight": "model-00001-of-00004.safetensors", "model.embed_audio.embedding.weight": "model-00001-of-00004.safetensors", "model.embed_audio.embedding_projection.weight": "model-00002-of-00004.safetensors", "model.embed_audio.hard_embedding_norm.weight": "model-00002-of-00004.safetensors", "model.embed_audio.soft_embedding_norm.weight": "model-00002-of-00004.safetensors", "model.embed_vision.embedding.weight": "model-00001-of-00004.safetensors", "model.embed_vision.embedding_projection.weight": "model-00002-of-00004.safetensors", "model.embed_vision.hard_embedding_norm.weight": "model-00002-of-00004.safetensors", "model.embed_vision.soft_embedding_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.altup_projections.0.weight": "model-00001-of-00004.safetensors", "model.language_model.altup_projections.1.weight": "model-00001-of-00004.safetensors", "model.language_model.altup_projections.2.weight": "model-00001-of-00004.safetensors", "model.language_model.altup_unembed_projections.0.weight": "model-00001-of-00004.safetensors", "model.language_model.altup_unembed_projections.1.weight": "model-00001-of-00004.safetensors", "model.language_model.altup_unembed_projections.2.weight": "model-00001-of-00004.safetensors", "model.language_model.embed_tokens.weight": "model-00001-of-00004.safetensors", "model.language_model.embed_tokens_per_layer.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.altup.correct_output_scale": "model-00002-of-00004.safetensors", "model.language_model.layers.0.altup.correction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.altup.modality_router.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.altup.prediction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.altup.router_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.laurel.linear_left.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.laurel.linear_right.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.0.self_attn.k_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.self_attn.q_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.0.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.1.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.1.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.1.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.altup.correct_output_scale": "model-00002-of-00004.safetensors", "model.language_model.layers.10.altup.correction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.altup.modality_router.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.altup.prediction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.altup.router_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.laurel.linear_left.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.laurel.linear_right.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.10.self_attn.k_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.self_attn.q_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.10.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.11.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.11.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.11.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.12.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.12.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.13.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.13.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.13.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.14.altup.correct_output_scale": "model-00004-of-00004.safetensors", "model.language_model.layers.14.altup.correction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.altup.modality_router.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.altup.prediction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.altup.router_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.laurel.linear_left.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.laurel.linear_right.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.self_attn.k_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.self_attn.q_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.14.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.15.altup.correct_output_scale": "model-00002-of-00004.safetensors", "model.language_model.layers.15.altup.correction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.altup.modality_router.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.altup.prediction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.altup.router_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.laurel.linear_left.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.laurel.linear_right.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.15.self_attn.k_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.self_attn.q_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.15.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.16.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.16.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.16.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.17.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.17.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.18.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.18.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.18.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.19.altup.correct_output_scale": "model-00004-of-00004.safetensors", "model.language_model.layers.19.altup.correction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.altup.modality_router.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.altup.prediction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.altup.router_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.laurel.linear_left.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.laurel.linear_right.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.self_attn.k_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.self_attn.q_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.19.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.2.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.2.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.2.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.altup.correct_output_scale": "model-00002-of-00004.safetensors", "model.language_model.layers.20.altup.correction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.altup.modality_router.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.altup.prediction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.altup.router_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.laurel.linear_left.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.laurel.linear_right.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.20.self_attn.k_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.self_attn.q_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.20.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.21.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.21.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.21.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.22.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.22.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.23.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.23.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.23.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.24.altup.correct_output_scale": "model-00004-of-00004.safetensors", "model.language_model.layers.24.altup.correction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.altup.modality_router.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.altup.prediction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.altup.router_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.laurel.linear_left.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.laurel.linear_right.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.self_attn.k_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.self_attn.q_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.24.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.25.altup.correct_output_scale": "model-00002-of-00004.safetensors", "model.language_model.layers.25.altup.correction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.altup.modality_router.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.altup.prediction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.altup.router_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.laurel.linear_left.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.laurel.linear_right.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.25.self_attn.k_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.self_attn.q_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.25.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.26.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.26.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.26.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.27.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.27.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.28.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.28.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.28.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.29.altup.correct_output_scale": "model-00004-of-00004.safetensors", "model.language_model.layers.29.altup.correction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.altup.modality_router.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.altup.prediction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.altup.router_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.laurel.linear_left.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.laurel.linear_right.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.self_attn.k_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.self_attn.q_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.29.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.3.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.3.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.3.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.altup.correct_output_scale": "model-00002-of-00004.safetensors", "model.language_model.layers.30.altup.correction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.altup.modality_router.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.altup.prediction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.altup.router_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.laurel.linear_left.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.laurel.linear_right.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.30.self_attn.k_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.self_attn.q_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.30.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.31.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.31.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.31.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.32.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.32.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.33.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.33.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.33.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.34.altup.correct_output_scale": "model-00004-of-00004.safetensors", "model.language_model.layers.34.altup.correction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.altup.modality_router.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.altup.prediction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.altup.router_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.laurel.linear_left.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.laurel.linear_right.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.self_attn.k_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.self_attn.q_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.34.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.altup.correct_output_scale": "model-00004-of-00004.safetensors", "model.language_model.layers.4.altup.correction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.altup.modality_router.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.altup.prediction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.altup.router_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.laurel.linear_left.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.laurel.linear_right.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.self_attn.k_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.self_attn.q_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.4.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.5.altup.correct_output_scale": "model-00002-of-00004.safetensors", "model.language_model.layers.5.altup.correction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.altup.modality_router.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.altup.prediction_coefs.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.altup.router_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.laurel.linear_left.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.laurel.linear_right.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.5.self_attn.k_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.self_attn.q_norm.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.5.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "model.language_model.layers.6.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.6.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.6.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.7.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.input_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.laurel.post_laurel_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.per_layer_input_gate.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.per_layer_projection.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.post_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.post_per_layer_input_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.pre_feedforward_layernorm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.7.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.altup.correct_output_scale": "model-00003-of-00004.safetensors", "model.language_model.layers.8.altup.correction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.altup.modality_router.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.altup.prediction_coefs.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.altup.router_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.8.laurel.linear_left.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.laurel.linear_right.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.8.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.8.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.8.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.8.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.8.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.8.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.8.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.8.self_attn.k_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.self_attn.q_norm.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.8.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "model.language_model.layers.9.altup.correct_output_scale": "model-00004-of-00004.safetensors", "model.language_model.layers.9.altup.correction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.altup.modality_router.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.altup.prediction_coefs.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.altup.router_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.input_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.laurel.linear_left.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.laurel.linear_right.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.laurel.post_laurel_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.mlp.down_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.mlp.gate_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.mlp.up_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.per_layer_input_gate.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.per_layer_projection.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.post_attention_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.post_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.post_per_layer_input_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.pre_feedforward_layernorm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.self_attn.k_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.self_attn.k_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.self_attn.o_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.self_attn.q_norm.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.self_attn.q_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.layers.9.self_attn.v_proj.weight": "model-00004-of-00004.safetensors", "model.language_model.norm.weight": "model-00002-of-00004.safetensors", "model.language_model.per_layer_model_projection.weight": "model-00002-of-00004.safetensors", "model.language_model.per_layer_projection_norm.weight": "model-00002-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.0.bn1.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.0.bn2.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.0.conv_exp.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.0.conv_pwl.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.1.bn1.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.1.bn2.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.1.conv_exp.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.1.conv_pwl.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.2.bn1.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.2.bn2.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.2.conv_exp.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.0.2.conv_pwl.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.0.dw_mid.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.0.dw_mid.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.0.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.0.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.0.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.0.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.0.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.0.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.0.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.1.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.1.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.1.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.1.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.1.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.1.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.1.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.2.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.2.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.2.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.2.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.2.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.2.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.2.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.3.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.3.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.3.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.3.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.3.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.3.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.3.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.4.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.4.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.4.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.4.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.4.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.4.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.1.4.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.0.dw_mid.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.0.dw_mid.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.0.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.0.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.0.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.0.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.0.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.0.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.0.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.1.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.1.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.1.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.1.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.1.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.1.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.1.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.10.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.10.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.10.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.10.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.10.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.11.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.12.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.12.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.12.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.12.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.12.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.13.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.14.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.14.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.14.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.14.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.14.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.15.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.16.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.16.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.16.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.16.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.16.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.17.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.18.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.18.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.18.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.18.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.18.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.19.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.2.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.2.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.2.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.2.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.2.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.2.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.2.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.20.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.20.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.20.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.20.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.20.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.21.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.22.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.22.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.22.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.22.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.22.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.23.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.24.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.24.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.24.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.24.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.24.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.25.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.26.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.26.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.26.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.26.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.26.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.27.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.28.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.28.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.28.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.28.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.28.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.29.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.3.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.3.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.3.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.3.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.3.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.3.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.3.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.30.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.30.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.30.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.30.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.30.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.31.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.32.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.32.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.32.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.32.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.32.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.33.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.34.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.34.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.34.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.34.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.34.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.35.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.36.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.36.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.36.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.36.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.36.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.4.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.4.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.4.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.4.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.4.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.4.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.4.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.5.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.5.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.5.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.5.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.5.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.5.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.5.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.6.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.6.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.6.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.6.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.6.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.6.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.6.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.7.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.7.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.7.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.7.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.7.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.7.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.7.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.8.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.8.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.8.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.8.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.8.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.attn.key.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.attn.key.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.attn.value.down_conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.attn.value.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.2.9.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.0.dw_mid.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.0.dw_mid.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.0.dw_start.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.0.dw_start.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.0.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.0.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.0.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.0.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.0.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.1.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.1.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.1.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.1.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.1.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.1.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.10.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.10.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.10.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.10.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.10.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.11.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.11.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.11.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.11.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.11.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.11.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.12.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.12.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.12.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.12.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.12.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.13.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.13.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.13.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.13.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.13.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.13.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.14.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.14.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.14.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.14.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.14.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.15.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.15.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.15.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.15.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.15.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.15.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.16.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.16.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.16.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.16.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.16.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.17.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.17.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.17.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.17.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.17.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.17.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.18.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.18.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.18.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.18.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.18.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.19.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.19.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.19.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.19.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.19.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.19.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.2.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.2.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.2.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.2.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.2.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.20.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.20.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.20.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.20.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.20.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.21.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.21.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.21.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.21.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.21.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.21.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.22.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.22.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.22.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.22.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.22.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.23.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.23.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.23.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.23.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.23.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.23.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.24.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.24.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.24.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.24.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.24.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.25.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.25.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.25.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.25.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.25.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.25.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.26.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.26.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.26.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.26.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.26.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.27.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.27.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.27.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.27.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.27.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.27.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.28.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.28.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.28.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.28.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.28.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.29.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.29.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.29.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.29.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.29.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.29.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.3.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.3.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.3.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.3.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.3.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.3.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.30.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.30.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.30.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.30.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.30.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.31.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.31.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.31.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.31.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.31.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.31.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.32.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.32.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.32.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.32.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.32.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.33.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.33.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.33.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.33.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.33.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.33.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.34.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.34.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.34.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.34.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.34.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.35.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.35.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.35.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.35.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.35.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.35.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.36.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.36.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.36.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.36.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.36.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.37.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.37.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.37.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.37.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.37.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.37.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.38.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.38.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.38.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.38.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.38.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.4.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.4.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.4.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.4.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.4.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.5.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.5.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.5.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.5.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.5.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.5.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.6.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.6.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.6.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.6.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.6.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.7.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.7.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.7.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.7.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.7.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.7.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.8.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.8.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.8.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.8.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.8.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.9.attn.key.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.9.attn.output.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.9.attn.query.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.9.attn.value.proj.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.9.layer_scale.gamma": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.blocks.3.9.norm.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.conv_stem.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.conv_stem.conv.bias": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.conv_stem.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.msfa.ffn.pw_exp.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.msfa.ffn.pw_exp.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.msfa.ffn.pw_proj.bn.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.msfa.ffn.pw_proj.conv.weight": "model-00001-of-00004.safetensors", "model.vision_tower.timm_model.msfa.norm.weight": "model-00001-of-00004.safetensors"}}