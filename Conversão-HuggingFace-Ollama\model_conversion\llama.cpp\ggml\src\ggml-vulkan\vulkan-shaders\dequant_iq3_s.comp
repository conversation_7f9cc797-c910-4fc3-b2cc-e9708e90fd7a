#version 450

#include "dequant_head.comp"

layout(local_size_x = 256, local_size_y = 1, local_size_z = 1) in;

layout (binding = 0) readonly buffer A {block_iq3_s data_a[];};
layout (binding = 1) writeonly buffer D {D_TYPE data_b[];};

void main() {
    // Each thread handles 1 scale nibble.
    // Each block contains 4 scale bytes (8 scales) for 256 output values.
    const uint ib = gl_WorkGroupID.x * 32 + gl_LocalInvocationID.x / 8;

    init_iq_shmem(gl_WorkGroupSize);

    if (ib >= p.nel / 256) {
        return;
    }

    const uint is = gl_LocalInvocationID.x % 8;
    const uint b_idx = 256 * ib + 32 * is;

    const float d = float(data_a[ib].d);
    const float db = d * (1 + 2 * ((data_a[ib].scales[is] >> (4 * (is % 2))) & 0xf));

    // We must produce 32 values using 4 sign bytes, 1 qh byte, 8 qs bytes.
    uint qh = data_a[ib].qh[is];
    [[unroll]] for (uint l = 0; l < 8; ++l) {
        uint qs = data_a[ib].qs[8 * is + l];
        uint gidx = qs | ((qh << (8 - l)) & 256);
        uint8_t signs = data_a[ib].signs[8 * is + l / 2] >> (4 * (l & 1));
        u8vec4 grid = unpack8(iq3s_grid[gidx]);
        data_b[b_idx + 4 * l + 0] = D_TYPE(db * grid.x * ((signs & 1) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 4 * l + 1] = D_TYPE(db * grid.y * ((signs & 2) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 4 * l + 2] = D_TYPE(db * grid.z * ((signs & 4) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 4 * l + 3] = D_TYPE(db * grid.w * ((signs & 8) != 0 ? -1.0 : 1.0));
    }
}
